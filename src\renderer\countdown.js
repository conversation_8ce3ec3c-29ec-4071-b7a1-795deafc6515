class CountdownManager {
    constructor() {
        this.totalTime = 5 * 60; // 5分钟，单位：秒
        this.remainingTime = this.totalTime;
        this.isRunning = true;
        this.isCancelled = false;

        // DOM 元素
        this.countdownTimeEl = document.getElementById('countdownTime');
        this.progressCircleEl = document.getElementById('progressCircle');
        this.progressPercentageEl = document.getElementById('progressPercentage');
        this.slideButtonEl = document.getElementById('slideButton');
        this.slideProgressEl = document.getElementById('slideProgress');
        this.slideTextEl = document.getElementById('slideText');
        this.statusMessageEl = document.getElementById('statusMessage');
        this.containerEl = document.querySelector('.countdown-container');

        // 滑动相关变量
        this.isDragging = false;
        this.startX = 0;
        this.currentX = 0;
        this.slideTrackWidth = 0;
        this.buttonWidth = 52;
        this.maxSlideDistance = 0;
        this.holdTimer = null;
        this.holdDuration = 1000; // 1秒
        this.isHolding = false;

        // 进度环配置
        this.circleRadius = 90;
        this.circleCircumference = 2 * Math.PI * this.circleRadius;

        this.init();
    }

    init() {
        this.setupProgressRing();
        this.setupSlideToCancel();
        this.startCountdown();
        this.updateDisplay();
    }

    setupProgressRing() {
        this.progressCircleEl.style.strokeDasharray = this.circleCircumference;
        this.progressCircleEl.style.strokeDashoffset = 0;
    }

    setupSlideToCancel() {
        const slideTrack = document.querySelector('.slide-track');
        this.slideTrackWidth = slideTrack.offsetWidth;
        this.maxSlideDistance = this.slideTrackWidth - this.buttonWidth - 8; // 8px for padding

        // 鼠标事件
        this.slideButtonEl.addEventListener('mousedown', this.handleStart.bind(this));
        document.addEventListener('mousemove', this.handleMove.bind(this));
        document.addEventListener('mouseup', this.handleEnd.bind(this));

        // 触摸事件
        this.slideButtonEl.addEventListener('touchstart', this.handleStart.bind(this));
        document.addEventListener('touchmove', this.handleMove.bind(this));
        document.addEventListener('touchend', this.handleEnd.bind(this));

        // 防止默认拖拽行为
        this.slideButtonEl.addEventListener('dragstart', (e) => e.preventDefault());
    }

    handleStart(e) {
        if (this.isCancelled) return;

        e.preventDefault();
        this.isDragging = true;
        this.isHolding = false;

        const clientX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
        this.startX = clientX - this.slideButtonEl.offsetLeft;

        this.slideButtonEl.classList.add('dragging');
        this.addRippleEffect();

        // 清除之前的定时器
        if (this.holdTimer) {
            clearTimeout(this.holdTimer);
        }
    }

    handleMove(e) {
        if (!this.isDragging || this.isCancelled) return;

        e.preventDefault();
        const clientX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
        this.currentX = clientX - this.startX;

        // 限制滑动范围（只能向左滑动）
        this.currentX = Math.max(Math.min(this.currentX, 0), -this.maxSlideDistance);

        // 更新按钮位置
        this.slideButtonEl.style.transform = `translateX(${this.currentX}px)`;

        // 更新进度条
        const progress = Math.abs(this.currentX) / this.maxSlideDistance;
        this.slideProgressEl.style.width = `${progress * 100}%`;

        // 更新文本透明度
        this.slideTextEl.style.opacity = 1 - progress * 0.7;

        // 检查是否滑动到足够距离
        if (Math.abs(this.currentX) >= this.maxSlideDistance * 0.8) {
            if (!this.isHolding) {
                this.startHoldTimer();
            }
        } else {
            this.stopHoldTimer();
        }
    }

    handleEnd(e) {
        if (!this.isDragging || this.isCancelled) return;

        this.isDragging = false;
        this.slideButtonEl.classList.remove('dragging');

        // 如果没有完成取消操作，回弹
        if (!this.isHolding || Math.abs(this.currentX) < this.maxSlideDistance * 0.8) {
            this.bounceBack();
        }

        this.stopHoldTimer();
    }

    startHoldTimer() {
        this.isHolding = true;
        this.slideButtonEl.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';

        this.holdTimer = setTimeout(() => {
            if (this.isHolding && Math.abs(this.currentX) >= this.maxSlideDistance * 0.8) {
                this.cancelShutdown();
            }
        }, this.holdDuration);
    }

    stopHoldTimer() {
        this.isHolding = false;
        this.slideButtonEl.style.background = 'linear-gradient(135deg, #fff, #f0f0f0)';

        if (this.holdTimer) {
            clearTimeout(this.holdTimer);
            this.holdTimer = null;
        }
    }

    bounceBack() {
        this.slideButtonEl.classList.add('bounce-back');
        this.slideButtonEl.style.setProperty('--drag-distance', `${this.currentX}px`);
        this.slideButtonEl.style.transform = 'translateX(0px)';
        this.slideProgressEl.style.width = '0%';
        this.slideTextEl.style.opacity = '1';
        this.currentX = 0;

        setTimeout(() => {
            this.slideButtonEl.classList.remove('bounce-back');
        }, 500);
    }

    addRippleEffect() {
        const ripple = this.slideButtonEl.querySelector('.slide-button-ripple');
        ripple.classList.remove('active');
        setTimeout(() => ripple.classList.add('active'), 10);
    }

    cancelShutdown() {
        this.isCancelled = true;
        this.isRunning = false;

        // 显示成功动画
        this.slideButtonEl.classList.add('success');
        this.slideProgressEl.style.width = '100%';
        this.slideProgressEl.style.background = 'linear-gradient(90deg, #4CAF50, #45a049)';

        // 显示状态消息
        this.showStatusMessage('关机已取消', 'success');

        // 添加取消动画
        setTimeout(() => {
            this.containerEl.classList.add('cancelled');
        }, 1000);

        // 通知主进程
        if (window.electronAPI) {
            window.electronAPI.cancelShutdown();
        }

        // 2秒后关闭窗口
        setTimeout(() => {
            if (window.electronAPI) {
                window.electronAPI.closeWindow();
            }
        }, 2000);
    }

    startCountdown() {
        const timer = setInterval(() => {
            if (!this.isRunning || this.isCancelled) {
                clearInterval(timer);
                return;
            }

            this.remainingTime--;
            this.updateDisplay();

            // 检查是否到时间
            if (this.remainingTime <= 0) {
                clearInterval(timer);
                this.executeShutdown();
            }

            // 最后30秒进入紧急状态
            if (this.remainingTime <= 30 && !this.containerEl.classList.contains('urgent')) {
                this.containerEl.classList.add('urgent');
                this.showStatusMessage('警告：系统即将关机！', 'warning');
            }
        }, 1000);
    }

    updateDisplay() {
        // 更新倒计时显示
        const minutes = Math.floor(this.remainingTime / 60);
        const seconds = this.remainingTime % 60;
        this.countdownTimeEl.textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // 更新进度环
        const progress = (this.totalTime - this.remainingTime) / this.totalTime;
        const offset = this.circleCircumference * (1 - progress);
        this.progressCircleEl.style.strokeDashoffset = offset;

        // 更新百分比
        const percentage = Math.round((this.remainingTime / this.totalTime) * 100);
        this.progressPercentageEl.textContent = `${percentage}%`;

        // 自动滑动效果（随时间推移）
        if (!this.isDragging && !this.isCancelled) {
            const autoSlideProgress = (this.totalTime - this.remainingTime) / this.totalTime;
            const autoSlideWidth = Math.min(autoSlideProgress * 20, 15); // 最多滑动15%

            // 只在没有用户交互时显示自动滑动
            if (autoSlideWidth > 0) {
                this.slideProgressEl.style.width = `${autoSlideWidth}%`;
                this.slideProgressEl.style.background = 'linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1))';
            }
        }
    }

    executeShutdown() {
        this.showStatusMessage('正在关机...', 'info');

        // 通知主进程执行关机
        if (window.electronAPI) {
            window.electronAPI.executeShutdown();
        }
    }

    showStatusMessage(message, type = 'info') {
        this.statusMessageEl.textContent = message;
        this.statusMessageEl.className = `status-message ${type}`;
        this.statusMessageEl.classList.add('show');

        // 3秒后自动隐藏（除非是成功消息）
        if (type !== 'success') {
            setTimeout(() => {
                this.statusMessageEl.classList.remove('show');
            }, 3000);
        }
    }

    // 处理窗口大小变化
    handleResize() {
        const slideTrack = document.querySelector('.slide-track');
        this.slideTrackWidth = slideTrack.offsetWidth;
        this.maxSlideDistance = this.slideTrackWidth - this.buttonWidth - 8;
    }
}

// 工具函数
class AnimationUtils {
    static easeOutBounce(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    }

    static animate(element, property, from, to, duration, easing = 'ease') {
        const start = performance.now();

        function update(currentTime) {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);

            let easedProgress = progress;
            if (easing === 'bounce') {
                easedProgress = AnimationUtils.easeOutBounce(progress);
            }

            const value = from + (to - from) * easedProgress;
            element.style[property] = `${value}px`;

            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }

        requestAnimationFrame(update);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    const countdownManager = new CountdownManager();

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        countdownManager.handleResize();
    });

    // 监听键盘事件
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            countdownManager.cancelShutdown();
        }
    });

    // 防止右键菜单
    document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });

    // 防止选择文本
    document.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });
});