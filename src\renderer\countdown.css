/* countdown.css */
body {
    margin: 0;
    overflow: hidden; /* 防止滚动条 */
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #2a003f, #001f3f, #003f3f); /* 炫酷渐变背景 */
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #fff;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
    animation: backgroundShift 20s infinite alternate; /* 背景动画 */
}

@keyframes backgroundShift {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
    padding: 30px;
    background: rgba(0, 0, 0, 0.4); /* 半透明背景 */
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
}

/* 古诗词样式 */
.poem-display {
    text-align: center;
    font-size: 2.2em;
    font-style: italic;
    margin-bottom: 20px;
    line-height: 1.5;
    max-width: 80vw;
    opacity: 0; /* 初始隐藏 */
    animation: fadeIn 2s forwards; /* 渐入动画 */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 倒计时和提示信息 */
.shutdown-info {
    text-align: center;
}

#countdown {
    font-size: 5em;
    font-weight: bold;
    color: #ffda47; /* 亮黄色 */
    text-shadow: 0 0 15px #ffda47;
    animation: pulsate 1.5s infinite alternate; /* 跳动效果 */
}

@keyframes pulsate {
    from { transform: scale(1); }
    to { transform: scale(1.05); }
}

.shutdown-info p {
    font-size: 1.5em;
    margin-top: 10px;
    color: rgba(255, 255, 255, 0.9);
}

/* 滑动解锁区域 */
.slide-to-cancel-container {
    width: 80vw;
    max-width: 500px;
    height: 70px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 35px;
    position: relative;
    overflow: hidden;
    margin-top: 30px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.slider-track {
    width: 100%;
    height: 100%;
    position: relative;
}

.slider-fill {
    height: 100%;
    width: 0%; /* 初始宽度为0 */
    background: linear-gradient(90deg, #4CAF50, #8BC34A); /* 绿色渐变填充 */
    border-radius: 35px;
    position: absolute;
    left: 0;
    top: 0;
    transition: width 0.1s linear; /* 平滑过渡 */
}

.slider-thumb {
    height: calc(100% - 10px);
    width: 150px; /* 滑块宽度 */
    background: #ffffff;
    border-radius: 30px;
    position: absolute;
    left: 5px; /* 初始位置 */
    top: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
    font-weight: bold;
    font-size: 1.1em;
    cursor: grab; /* 抓手图标 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: left 0.1s ease-out; /* 滑块移动过渡 */
    user-select: none; /* 禁止选择文本 */
}

.slide-to-cancel-container.locked .slider-thumb {
    transition: none; /* 锁定状态下取消动画 */
}

.slider-thumb:active {
    cursor: grabbing; /* 拖拽时变为抓取中图标 */
}

.cancel-message {
    position: absolute;
    bottom: -30px; /* 初始隐藏在下方 */
    left: 50%;
    transform: translateX(-50%);
    color: #ff4d4d; /* 红色 */
    font-weight: bold;
    font-size: 1.2em;
    opacity: 0;
    transition: all 0.3s ease-out;
}

.cancel-message.visible {
    bottom: 80px; /* 显示位置 */
    opacity: 1;
}