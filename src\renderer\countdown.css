/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
    height: 100vh;
    user-select: none;
}

/* 主容器 */
.countdown-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 40px 20px;
}

/* 背景动画粒子 */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
    animation-duration: 6s;
}

.particle:nth-child(3) {
    top: 80%;
    left: 40%;
    animation-delay: 4s;
    animation-duration: 7s;
}

.particle:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 1s;
    animation-duration: 9s;
}

.particle:nth-child(5) {
    top: 70%;
    left: 10%;
    animation-delay: 3s;
    animation-duration: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 0.8;
    }
}

/* 主要内容区域 */
.main-content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    flex: 1;
    justify-content: center;
}

/* 系统图标 */
.system-icon {
    margin-bottom: 20px;
}

.power-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: pulse 2s ease-in-out infinite;
}

.power-icon svg {
    width: 40px;
    height: 40px;
    color: #fff;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    }
}

/* 倒计时显示 */
.countdown-display {
    text-align: center;
    margin-bottom: 30px;
}

.countdown-title {
    font-size: 18px;
    font-weight: 300;
    margin-bottom: 15px;
    opacity: 0.9;
}

.countdown-time {
    font-size: 72px;
    font-weight: 100;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    animation: timeGlow 2s ease-in-out infinite alternate;
}

.countdown-subtitle {
    font-size: 16px;
    font-weight: 300;
    opacity: 0.8;
}

@keyframes timeGlow {
    from {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    }
    to {
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
    }
}

/* 进度环 */
.progress-ring {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-ring-svg {
    transform: rotate(-90deg);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.progress-ring-background {
    fill: none;
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: 4;
}

.progress-ring-progress {
    fill: none;
    stroke: #fff;
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 565.48;
    stroke-dashoffset: 0;
    transition: stroke-dashoffset 0.5s ease;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
}

.progress-percentage {
    position: absolute;
    font-size: 24px;
    font-weight: 300;
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* 滑动解锁区域 */
.slide-to-cancel {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 400px;
    margin-bottom: 20px;
}

.slide-track {
    position: relative;
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.slide-track:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.slide-track-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(100%);
    }
}

.slide-track-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #ee5a24);
    border-radius: 30px;
    width: 0%;
    transition: width 0.1s ease;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.5);
}

.slide-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    font-weight: 400;
    color: white;
    pointer-events: none;
    transition: opacity 0.3s ease;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.slide-button {
    position: absolute;
    top: 4px;
    left: 4px;
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #fff, #f0f0f0);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.slide-button:active {
    cursor: grabbing;
    transform: scale(1.1);
}

.slide-button-icon {
    width: 24px;
    height: 24px;
    color: #666;
    transition: transform 0.3s ease;
}

.slide-button-ripple {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    opacity: 0;
    pointer-events: none;
}

.slide-hint {
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 15px;
    font-weight: 300;
}

/* 状态消息 */
.status-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px 40px;
    border-radius: 10px;
    font-size: 18px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.status-message.show {
    opacity: 1;
    visibility: visible;
}

/* 滑动动画效果 */
.slide-button.dragging {
    transition: none;
}

.slide-button.bounce-back {
    animation: bounceBack 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceBack {
    0% {
        transform: translateX(var(--drag-distance, 0px));
    }
    100% {
        transform: translateX(0px);
    }
}

.slide-button.success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

.slide-button-ripple.active {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* 取消成功动画 */
.countdown-container.cancelled {
    animation: fadeOut 1s ease-in-out;
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* 紧急状态样式 */
.countdown-container.urgent .countdown-time {
    color: #ff6b6b;
    animation: urgentBlink 1s ease-in-out infinite;
}

.countdown-container.urgent .progress-ring-progress {
    stroke: #ff6b6b;
    animation: urgentGlow 1s ease-in-out infinite;
}

@keyframes urgentBlink {
    0%, 50% {
        opacity: 1;
        text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
    }
    25%, 75% {
        opacity: 0.7;
        text-shadow: 0 0 30px rgba(255, 107, 107, 1);
    }
}

@keyframes urgentGlow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(255, 107, 107, 0.8));
    }
    50% {
        filter: drop-shadow(0 0 15px rgba(255, 107, 107, 1));
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .countdown-container {
        padding: 20px 15px;
    }

    .countdown-time {
        font-size: 56px;
    }

    .power-icon {
        width: 60px;
        height: 60px;
    }

    .power-icon svg {
        width: 30px;
        height: 30px;
    }

    .progress-ring-svg {
        width: 160px;
        height: 160px;
    }

    .slide-track {
        height: 50px;
        border-radius: 25px;
    }

    .slide-button {
        width: 42px;
        height: 42px;
        top: 4px;
        left: 4px;
    }

    .slide-text {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .countdown-time {
        font-size: 48px;
    }

    .countdown-title {
        font-size: 16px;
    }

    .countdown-subtitle {
        font-size: 14px;
    }

    .progress-ring-svg {
        width: 140px;
        height: 140px;
    }

    .progress-percentage {
        font-size: 20px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .slide-track {
        border: 2px solid white;
    }

    .slide-button {
        border: 2px solid #333;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}